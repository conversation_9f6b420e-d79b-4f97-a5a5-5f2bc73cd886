#ifndef ZOOMABLEGRAPHICSVIEW_H
#define ZOOMABLEGRAPHICSVIEW_H

#include <QGraphicsView>
#include <QWheelEvent>
#include <QDebug>

class ZoomableGraphicsView : public QGraphicsView
{
    Q_OBJECT

public:
    explicit ZoomableGraphicsView(QWidget* parent = nullptr);
    explicit ZoomableGraphicsView(QGraphicsScene* scene, QWidget* parent = nullptr);

signals:
    void zoomChanged(double zoomLevel);

protected:
    void wheelEvent(QWheelEvent* event) override;

private:
    double m_minZoom;
    double m_maxZoom;
    double m_zoomFactor;
};

#endif // ZOOMABLEGRAPHICSVIEW_H
