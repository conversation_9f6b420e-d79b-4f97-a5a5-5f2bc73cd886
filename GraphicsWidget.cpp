#include "GraphicsWidget.h"
#include <QDebug>
#include <QApplication>

GraphicsWidget::GraphicsWidget(QWidget *parent)
    : QWidget(parent)
{
    // 初始化UI组件
    m_mainLayout = new QVBoxLayout(this);
    m_buttonLayout = new QHBoxLayout();
    
    // 创建按钮
    m_drawCurve1Btn = new QPushButton("绘制曲线1", this);
    m_drawCurve2Btn = new QPushButton("绘制曲线2", this);
    m_drawCurve3Btn = new QPushButton("绘制曲线3", this);
    m_clearBtn = new QPushButton("清除", this);
    m_resetViewBtn = new QPushButton("重置视图", this);
    m_toggleCoordBtn = new QPushButton("笛卡尔坐标系", this);
    m_viewTransformBtn = new QPushButton("视图翻转", this);
    
    // 添加按钮到布局
    m_buttonLayout->addWidget(m_drawCurve1Btn);
    m_buttonLayout->addWidget(m_drawCurve2Btn);
    m_buttonLayout->addWidget(m_drawCurve3Btn);
    m_buttonLayout->addWidget(m_clearBtn);
    m_buttonLayout->addWidget(m_resetViewBtn);
    m_buttonLayout->addWidget(m_toggleCoordBtn);
    m_buttonLayout->addWidget(m_viewTransformBtn);
    m_buttonLayout->addStretch();
    
    // 创建图形视图和场景
    m_scene = new QGraphicsScene(this);
    m_view = new ZoomableGraphicsView(m_scene, this);
    
    // 创建状态标签
    m_statusLabel = new QLabel("操作提示: 鼠标滚轮缩放，拖拽平移，点击按钮绘制曲线", this);
    m_statusLabel->setStyleSheet("QLabel { padding: 5px; background-color: #f0f0f0; border: 1px solid #ccc; }");

    // 添加到主布局
    m_mainLayout->addLayout(m_buttonLayout);
    m_mainLayout->addWidget(m_view);
    m_mainLayout->addWidget(m_statusLabel);
    
    // 设置绘制笔 - 使用更粗的线条以便在缩放后清晰可见
    m_pen = QPen(Qt::black, 1.5, Qt::SolidLine, Qt::RoundCap, Qt::RoundJoin);

    // 设置默认缩放因子（将大坐标缩放到合适的显示范围）
    m_scaleFactor = 1.0 / 10000.0; // 将万级坐标缩放到百位数，保持更好的精度

    // 默认启用Y轴翻转以匹配笛卡尔坐标系
    m_flipY = true;
    m_dataMinY = 0.0;
    m_dataMaxY = 0.0;

    // 连接信号槽
    connect(m_drawCurve1Btn, &QPushButton::clicked, this, &GraphicsWidget::onDrawCurve1);
    connect(m_drawCurve2Btn, &QPushButton::clicked, this, &GraphicsWidget::onDrawCurve2);
    connect(m_drawCurve3Btn, &QPushButton::clicked, this, &GraphicsWidget::onDrawCurve3);
    connect(m_clearBtn, &QPushButton::clicked, this, &GraphicsWidget::onClearScene);
    connect(m_resetViewBtn, &QPushButton::clicked, this, &GraphicsWidget::onResetView);
    connect(m_toggleCoordBtn, &QPushButton::clicked, this, &GraphicsWidget::onToggleCoordinateSystem);
    connect(m_viewTransformBtn, &QPushButton::clicked, this, &GraphicsWidget::onToggleViewTransform);
    connect(m_view, &ZoomableGraphicsView::zoomChanged, this, &GraphicsWidget::onZoomChanged);
    
    setWindowTitle("曲线绘制器");
    resize(1200, 800);
}

void GraphicsWidget::drawCurveData(const CurveData& curves)
{
    if (curves.empty()) return;

    // 计算最佳缩放因子
    calculateOptimalScale(curves);

    // 分析轮廓方向
    analyzeContourOrientation(curves);

    QPainterPath path;
    bool pathStarted = false;
    
    for (size_t i = 0; i < curves.size() - 1; ++i) {
        const CurvePoint& current = curves[i];
        const CurvePoint& next = curves[i + 1];

        // 使用缩放后的坐标
        QPointF p0 = scalePoint(current.point);
        QPointF p1 = scalePoint(next.point);
        double radius = scaleRadius(current.radius);

        qDebug() << QString("段%1: 起点=(%2, %3), 终点=(%4, %5), 半径=%6")
                    .arg(i).arg(p0.x()).arg(p0.y()).arg(p1.x()).arg(p1.y()).arg(radius);
        
        if (!pathStarted) {
            path.moveTo(p0);
            pathStarted = true;
        }
        
        if (qAbs(radius) < 1e-6) {
            // 直线段
            path.lineTo(p1);
        } else {
            // 圆弧段
            ArcParams arcParams = getArcParams(p0, p1, radius);
            if (arcParams.valid) {
                qDebug() << QString("  圆心=(%1, %2), 半径=%3, 起始角=%4, 结束角=%5")
                            .arg(arcParams.center.x()).arg(arcParams.center.y())
                            .arg(arcParams.radius).arg(arcParams.startAngle).arg(arcParams.endAngle);

                // 使用QPainterPath的arcTo方法
                QRectF rect(arcParams.center.x() - arcParams.radius,
                           arcParams.center.y() - arcParams.radius,
                           2 * arcParams.radius, 2 * arcParams.radius);

                double sweepLength = arcParams.endAngle - arcParams.startAngle;

                // 处理角度跨越问题
                if (radius < 0) {
                    // 负半径表示逆时针
                    if (sweepLength <= 0) {
                        sweepLength += 360;
                    }
                } else {
                    // 正半径表示顺时针
                    if (sweepLength >= 0) {
                        sweepLength -= 360;
                    }
                }

                path.arcTo(rect, arcParams.startAngle, sweepLength);
            } else {
                // 半径太小，退化为直线
                path.lineTo(p1);
            }
        }
    }
    
    // 检查是否需要闭合路径
    QPointF lastPoint = scalePoint(curves.back().point);
    QPointF firstPoint = scalePoint(curves.front().point);
    if ((lastPoint - firstPoint).manhattanLength() > 1e-6) {
        path.lineTo(firstPoint);
    }
    
    // 添加路径到场景
    QGraphicsPathItem* pathItem = m_scene->addPath(path, m_pen);

    // 如果启用Y轴翻转，应用变换
    if (m_flipY) {
        // 使用painter变换来翻转Y轴 - 最简单的方法！
        QTransform flipTransform = QTransform::fromScale(1, -1);
        pathItem->setTransform(flipTransform);

        qDebug() << "已应用Y轴翻转变换";
    }

    // 输出调试信息
    QRectF boundingRect = m_scene->itemsBoundingRect();
    qDebug() << "Scene bounding rect:" << boundingRect;
    qDebug() << "Path bounding rect:" << path.boundingRect();

    // 自动调整视图以适应内容
    if (!boundingRect.isEmpty()) {
        m_view->fitInView(boundingRect, Qt::KeepAspectRatio);
        m_view->scale(0.8, 0.8); // 稍微缩小一点以便看到边界和更好的显示效果

        // 确保视图质量
        m_view->setRenderHints(QPainter::Antialiasing | QPainter::SmoothPixmapTransform);

        // 更新状态显示
        updateStatusDisplay();
    }
}

ArcParams GraphicsWidget::getArcParams(const QPointF& p0, const QPointF& p1, double radius)
{
    // 计算弦长
    double dx = p1.x() - p0.x();
    double dy = p1.y() - p0.y();
    double chordLength = std::sqrt(dx * dx + dy * dy);
    
    if (std::abs(radius) < chordLength / 2) {
        return ArcParams(); // 无效的圆弧参数
    }
    
    // 计算弦中点
    QPointF midPoint((p0.x() + p1.x()) / 2, (p0.y() + p1.y()) / 2);
    
    // 计算从弦中点到圆心的距离
    double absRadius = std::abs(radius);
    double h = std::sqrt(absRadius * absRadius - (chordLength / 2) * (chordLength / 2));
    
    // 计算垂直于弦的单位向量
    double perpDx = dy / chordLength;
    double perpDy = -dx / chordLength;
    
    // 根据半径正负确定圆心
    QPointF center;
    if (radius < 0) {
        // 负半径：逆时针方向
        center.setX(midPoint.x() - h * perpDx);
        center.setY(midPoint.y() - h * perpDy);
    } else {
        // 正半径：顺时针方向
        center.setX(midPoint.x() + h * perpDx);
        center.setY(midPoint.y() + h * perpDy);
    }
    
    // 计算起止角（以度为单位）
    double startAngle = radiansToDegrees(std::atan2(p0.y() - center.y(), p0.x() - center.x()));
    double endAngle = radiansToDegrees(std::atan2(p1.y() - center.y(), p1.x() - center.x()));
    
    return ArcParams(center, absRadius, startAngle, endAngle);
}

// 移除复杂的转换函数，现在在输入时预处理数据

double GraphicsWidget::radiansToDegrees(double radians)
{
    return radians * 180.0 / M_PI;
}

double GraphicsWidget::degreesToRadians(double degrees)
{
    return degrees * M_PI / 180.0;
}

void GraphicsWidget::clearScene()
{
    m_scene->clear();
}

void GraphicsWidget::onDrawCurve1()
{
    clearScene();
    drawCurveData(CurveDataSets::getCurves1());
}

void GraphicsWidget::onDrawCurve2()
{
    clearScene();
    drawCurveData(CurveDataSets::getCurves2());
}

void GraphicsWidget::onDrawCurve3()
{
    clearScene();
    drawCurveData(CurveDataSets::getCurves3());
}

void GraphicsWidget::onClearScene()
{
    clearScene();
}

void GraphicsWidget::onResetView()
{
    if (!m_scene->items().isEmpty()) {
        // 重置变换矩阵
        m_view->resetTransform();

        // 重新适应视图
        QRectF boundingRect = m_scene->itemsBoundingRect();
        if (!boundingRect.isEmpty()) {
            m_view->fitInView(boundingRect, Qt::KeepAspectRatio);
            m_view->scale(0.8, 0.8); // 稍微缩小一点以便看到边界
        }

        qDebug() << "视图已重置";
    }
}

QPointF GraphicsWidget::scalePoint(const QPointF& point)
{
    return QPointF(point.x() * m_scaleFactor, point.y() * m_scaleFactor);
}

double GraphicsWidget::scaleRadius(double radius)
{
    return radius * m_scaleFactor;
}

// transformPoint函数已移除，现在在preprocessCurveData中完成所有转换

void GraphicsWidget::calculateOptimalScale(const CurveData& curves)
{
    if (curves.empty()) return;

    // 计算坐标范围
    double minX = curves[0].point.x(), maxX = curves[0].point.x();
    double minY = curves[0].point.y(), maxY = curves[0].point.y();

    for (const auto& cp : curves) {
        minX = std::min(minX, cp.point.x());
        maxX = std::max(maxX, cp.point.x());
        minY = std::min(minY, cp.point.y());
        maxY = std::max(maxY, cp.point.y());
    }

    // 计算数据范围
    double rangeX = maxX - minX;
    double rangeY = maxY - minY;
    double maxRange = std::max(rangeX, rangeY);

    // 目标显示范围（像素）
    double targetRange = 1000.0; // 目标显示在1000像素范围内

    if (maxRange > 0) {
        m_scaleFactor = targetRange / maxRange;

        // 保存Y轴范围用于坐标系转换
        m_dataMinY = minY;
        m_dataMaxY = maxY;

        qDebug() << QString("数据范围: X[%1, %2], Y[%3, %4]")
                    .arg(minX).arg(maxX).arg(minY).arg(maxY);
        qDebug() << QString("最大范围: %1, 缩放因子: %2").arg(maxRange).arg(m_scaleFactor);
        qDebug() << QString("坐标系转换: Y轴翻转=%1").arg(m_flipY ? "是" : "否");

        // 根据缩放因子调整线条粗细，确保线条在任何缩放下都清晰可见
        double lineWidth = std::max(1.0, 2.0 / m_scaleFactor * 0.001); // 动态调整线宽
        lineWidth = std::min(lineWidth, 5.0); // 限制最大线宽
        m_pen.setWidthF(lineWidth);
        qDebug() << QString("调整线宽为: %1").arg(lineWidth);
    }
}

void GraphicsWidget::onZoomChanged(double zoomLevel)
{
    // 更新状态显示
    updateStatusDisplay();
}

void GraphicsWidget::onToggleCoordinateSystem()
{
    m_flipY = !m_flipY;

    // 更新按钮文本
    if (m_flipY) {
        m_toggleCoordBtn->setText("笛卡尔坐标系");
    } else {
        m_toggleCoordBtn->setText("Qt坐标系");
    }

    // 如果有图形，重新绘制
    if (!m_scene->items().isEmpty()) {
        // 保存当前显示的曲线数据并重新绘制
        // 这里简化处理，用户需要重新点击绘制按钮
        qDebug() << QString("坐标系已切换为: %1").arg(m_flipY ? "笛卡尔坐标系" : "Qt坐标系");

        // 更新状态显示
        QString coordSystem = m_flipY ? "笛卡尔坐标系(Y向上)" : "Qt坐标系(Y向下)";
        m_statusLabel->setText(QString("当前坐标系: %1 | 请重新绘制曲线以应用更改").arg(coordSystem));
    }
}

void GraphicsWidget::onToggleViewTransform()
{
    // 简单的视图层面Y轴翻转，不改变数据
    QTransform currentTransform = m_view->transform();

    // 检查当前是否已经翻转
    bool isFlipped = (currentTransform.m22() < 0);

    if (!isFlipped) {
        // 应用Y轴翻转
        if (!m_scene->items().isEmpty()) {
            QRectF bounds = m_scene->itemsBoundingRect();
            QTransform flipTransform;
            flipTransform.scale(1, -1);
            flipTransform.translate(0, -bounds.height());
            m_view->setTransform(flipTransform);
            m_viewTransformBtn->setText("取消翻转");
            m_statusLabel->setText("视图已翻转 (Y轴向上) | 数据未改变，仅显示翻转");
        }
    } else {
        // 取消翻转
        m_view->resetTransform();
        m_viewTransformBtn->setText("视图翻转");
        m_statusLabel->setText("视图已重置 (Y轴向下) | Qt原生坐标系");

        // 重新适应视图
        if (!m_scene->items().isEmpty()) {
            m_view->fitInView(m_scene->itemsBoundingRect(), Qt::KeepAspectRatio);
            m_view->scale(0.8, 0.8);
        }
    }

    qDebug() << QString("视图变换切换: %1").arg(isFlipped ? "已重置" : "已翻转");
}

bool GraphicsWidget::isClockwise(const CurveData& curves)
{
    if (curves.size() < 3) return false;

    // 使用Shoelace公式计算有向面积
    double area = 0.0;
    for (size_t i = 0; i < curves.size() - 1; i++) {
        const QPointF& p1 = curves[i].point;
        const QPointF& p2 = curves[i + 1].point;
        area += (p2.x() - p1.x()) * (p2.y() + p1.y());
    }

    // 在笛卡尔坐标系中，area > 0 表示顺时针
    // 在Qt坐标系中，area < 0 表示顺时针
    return area > 0;
}

void GraphicsWidget::analyzeContourOrientation(const CurveData& curves)
{
    if (curves.size() < 3) {
        qDebug() << "轮廓分析: 点数不足，无法判断方向";
        return;
    }

    bool clockwise = isClockwise(curves);

    qDebug() << "=== 轮廓方向分析 ===";
    qDebug() << QString("点数量: %1").arg(curves.size());
    qDebug() << QString("当前方向: %1").arg(clockwise ? "顺时针" : "逆时针");

    if (m_flipY) {
        // 笛卡尔坐标系语义
        qDebug() << QString("笛卡尔坐标系含义: %1").arg(clockwise ? "外轮廓" : "孔洞");
    } else {
        // Qt坐标系语义
        qDebug() << QString("Qt坐标系含义: %1").arg(clockwise ? "孔洞" : "外轮廓");
    }

    // 检查是否需要方向修正
    if (m_flipY) {
        // 在笛卡尔坐标系中，通常期望外轮廓是顺时针
        if (!clockwise) {
            qDebug() << "⚠️  警告: 当前为逆时针，可能是孔洞或需要方向修正";
        }
    }
}

CurveData GraphicsWidget::correctContourOrientation(const CurveData& curves, bool shouldBeClockwise)
{
    if (curves.size() < 3) return curves;

    bool currentlyClockwise = isClockwise(curves);

    if (currentlyClockwise == shouldBeClockwise) {
        return curves; // 方向已正确
    }

    // 需要反转方向
    CurveData corrected = curves;
    std::reverse(corrected.begin(), corrected.end());

    // 反转半径方向
    for (auto& point : corrected) {
        point.radius = -point.radius;
    }

    qDebug() << QString("轮廓方向已修正: %1 → %2")
                .arg(currentlyClockwise ? "顺时针" : "逆时针")
                .arg(shouldBeClockwise ? "顺时针" : "逆时针");

    return corrected;
}

// 预处理函数已移除，使用painter变换更简单

void GraphicsWidget::updateStatusDisplay()
{
    QTransform transform = m_view->transform();
    double currentScale = transform.m11();
    int scalePercent = static_cast<int>(currentScale * 100);

    QString statusText = QString("缩放: %1% | 操作: 鼠标滚轮缩放，拖拽平移")
                        .arg(scalePercent);

    m_statusLabel->setText(statusText);
}
