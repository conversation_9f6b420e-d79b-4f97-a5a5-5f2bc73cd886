#include "GraphicsWidget.h"
#include <QDebug>

GraphicsWidget::GraphicsWidget(QWidget *parent)
    : QWidget(parent)
{
    // 初始化UI组件
    m_mainLayout = new QVBoxLayout(this);
    m_buttonLayout = new QHBoxLayout();
    
    // 创建按钮
    m_drawCurve1Btn = new QPushButton("绘制曲线1", this);
    m_drawCurve2Btn = new QPushButton("绘制曲线2", this);
    m_drawCurve3Btn = new QPushButton("绘制曲线3", this);
    m_clearBtn = new QPushButton("清除", this);
    
    // 添加按钮到布局
    m_buttonLayout->addWidget(m_drawCurve1Btn);
    m_buttonLayout->addWidget(m_drawCurve2Btn);
    m_buttonLayout->addWidget(m_drawCurve3Btn);
    m_buttonLayout->addWidget(m_clearBtn);
    m_buttonLayout->addStretch();
    
    // 创建图形视图和场景
    m_scene = new QGraphicsScene(this);
    m_view = new QGraphicsView(m_scene, this);
    m_view->setRenderHint(QPainter::Antialiasing);
    m_view->setDragMode(QGraphicsView::ScrollHandDrag);
    
    // 添加到主布局
    m_mainLayout->addLayout(m_buttonLayout);
    m_mainLayout->addWidget(m_view);
    
    // 设置绘制笔
    m_pen = QPen(Qt::black, 2);
    
    // 连接信号槽
    connect(m_drawCurve1Btn, &QPushButton::clicked, this, &GraphicsWidget::onDrawCurve1);
    connect(m_drawCurve2Btn, &QPushButton::clicked, this, &GraphicsWidget::onDrawCurve2);
    connect(m_drawCurve3Btn, &QPushButton::clicked, this, &GraphicsWidget::onDrawCurve3);
    connect(m_clearBtn, &QPushButton::clicked, this, &GraphicsWidget::onClearScene);
    
    setWindowTitle("曲线绘制器");
    resize(1200, 800);
}

void GraphicsWidget::drawCurveData(const CurveData& curves)
{
    if (curves.empty()) return;
    
    QPainterPath path;
    bool pathStarted = false;
    
    for (size_t i = 0; i < curves.size() - 1; ++i) {
        const CurvePoint& current = curves[i];
        const CurvePoint& next = curves[i + 1];
        
        QPointF p0 = current.point;
        QPointF p1 = next.point;
        double radius = current.radius;
        
        qDebug() << QString("段%1: 起点=(%2, %3), 终点=(%4, %5), 半径=%6")
                    .arg(i).arg(p0.x()).arg(p0.y()).arg(p1.x()).arg(p1.y()).arg(radius);
        
        if (!pathStarted) {
            path.moveTo(p0);
            pathStarted = true;
        }
        
        if (qAbs(radius) < 1e-6) {
            // 直线段
            path.lineTo(p1);
        } else {
            // 圆弧段
            ArcParams arcParams = getArcParams(p0, p1, radius);
            if (arcParams.valid) {
                qDebug() << QString("  圆心=(%1, %2), 半径=%3, 起始角=%4, 结束角=%5")
                            .arg(arcParams.center.x()).arg(arcParams.center.y())
                            .arg(arcParams.radius).arg(arcParams.startAngle).arg(arcParams.endAngle);
                
                // 使用QPainterPath的arcTo方法
                QRectF rect(arcParams.center.x() - arcParams.radius,
                           arcParams.center.y() - arcParams.radius,
                           2 * arcParams.radius, 2 * arcParams.radius);
                
                double sweepLength = arcParams.endAngle - arcParams.startAngle;
                
                // 处理角度跨越问题
                if (radius < 0) {
                    // 负半径表示逆时针
                    if (sweepLength <= 0) {
                        sweepLength += 360;
                    }
                } else {
                    // 正半径表示顺时针
                    if (sweepLength >= 0) {
                        sweepLength -= 360;
                    }
                }
                
                path.arcTo(rect, arcParams.startAngle, sweepLength);
            } else {
                // 半径太小，退化为直线
                path.lineTo(p1);
            }
        }
    }
    
    // 检查是否需要闭合路径
    const QPointF& lastPoint = curves.back().point;
    const QPointF& firstPoint = curves.front().point;
    if ((lastPoint - firstPoint).manhattanLength() > 1e-6) {
        path.lineTo(firstPoint);
    }
    
    // 添加路径到场景
    QGraphicsPathItem* pathItem = m_scene->addPath(path, m_pen);
    
    // 自动调整视图以适应内容
    m_view->fitInView(m_scene->itemsBoundingRect(), Qt::KeepAspectRatio);
}

ArcParams GraphicsWidget::getArcParams(const QPointF& p0, const QPointF& p1, double radius)
{
    // 计算弦长
    double dx = p1.x() - p0.x();
    double dy = p1.y() - p0.y();
    double chordLength = std::sqrt(dx * dx + dy * dy);
    
    if (std::abs(radius) < chordLength / 2) {
        return ArcParams(); // 无效的圆弧参数
    }
    
    // 计算弦中点
    QPointF midPoint((p0.x() + p1.x()) / 2, (p0.y() + p1.y()) / 2);
    
    // 计算从弦中点到圆心的距离
    double absRadius = std::abs(radius);
    double h = std::sqrt(absRadius * absRadius - (chordLength / 2) * (chordLength / 2));
    
    // 计算垂直于弦的单位向量
    double perpDx = dy / chordLength;
    double perpDy = -dx / chordLength;
    
    // 根据半径正负确定圆心
    QPointF center;
    if (radius < 0) {
        // 负半径：逆时针方向
        center.setX(midPoint.x() - h * perpDx);
        center.setY(midPoint.y() - h * perpDy);
    } else {
        // 正半径：顺时针方向
        center.setX(midPoint.x() + h * perpDx);
        center.setY(midPoint.y() + h * perpDy);
    }
    
    // 计算起止角（以度为单位）
    double startAngle = radiansToDegrees(std::atan2(p0.y() - center.y(), p0.x() - center.x()));
    double endAngle = radiansToDegrees(std::atan2(p1.y() - center.y(), p1.x() - center.x()));
    
    return ArcParams(center, absRadius, startAngle, endAngle);
}

double GraphicsWidget::radiansToDegrees(double radians)
{
    return radians * 180.0 / M_PI;
}

double GraphicsWidget::degreesToRadians(double degrees)
{
    return degrees * M_PI / 180.0;
}

void GraphicsWidget::clearScene()
{
    m_scene->clear();
}

void GraphicsWidget::onDrawCurve1()
{
    clearScene();
    drawCurveData(CurveDataSets::getCurves1());
}

void GraphicsWidget::onDrawCurve2()
{
    clearScene();
    drawCurveData(CurveDataSets::getCurves2());
}

void GraphicsWidget::onDrawCurve3()
{
    clearScene();
    drawCurveData(CurveDataSets::getCurves3());
}

void GraphicsWidget::onClearScene()
{
    clearScene();
}
