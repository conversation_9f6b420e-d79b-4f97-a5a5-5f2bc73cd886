#ifndef GRAPHICSWIDGET_H
#define GRAPHICSWIDGET_H

#include <QWidget>
#include <QGraphicsView>
#include <QGraphicsScene>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QComboBox>
#include <QPainterPath>
#include <QGraphicsPathItem>
#include <cmath>
#include "CurveData.h"

struct ArcParams {
    QPointF center;
    double radius;
    double startAngle;
    double endAngle;
    bool valid;
    
    ArcParams() : valid(false) {}
    ArcParams(const QPointF& c, double r, double start, double end) 
        : center(c), radius(r), startAngle(start), endAngle(end), valid(true) {}
};

class GraphicsWidget : public QWidget
{
    Q_OBJECT

public:
    explicit GraphicsWidget(QWidget *parent = nullptr);

    // 绘制曲线数据
    void drawCurveData(const CurveData& curves);

    // 清除所有图形
    void clearScene();

    // 设置坐标缩放因子
    void setScaleFactor(double factor) { m_scaleFactor = factor; }

private slots:
    void onDrawCurve1();
    void onDrawCurve2();
    void onDrawCurve3();
    void onClearScene();

protected:
    void wheelEvent(QWheelEvent* event) override;

private:
    // 计算圆弧参数（从Python代码移植）
    ArcParams getArcParams(const QPointF& p0, const QPointF& p1, double radius);

    // 角度转换工具函数
    double radiansToDegrees(double radians);
    double degreesToRadians(double degrees);

    // 坐标缩放函数
    QPointF scalePoint(const QPointF& point);
    double scaleRadius(double radius);

    // 计算最佳缩放因子
    void calculateOptimalScale(const CurveData& curves);
    
    // UI组件
    QGraphicsView* m_view;
    QGraphicsScene* m_scene;
    QVBoxLayout* m_mainLayout;
    QHBoxLayout* m_buttonLayout;
    QPushButton* m_drawCurve1Btn;
    QPushButton* m_drawCurve2Btn;
    QPushButton* m_drawCurve3Btn;
    QPushButton* m_clearBtn;
    
    // 绘制相关
    QPen m_pen;
    double m_scaleFactor;
};

#endif // GRAPHICSWIDGET_H
