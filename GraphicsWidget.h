#ifndef GRAPHICSWIDGET_H
#define GRAPHICSWIDGET_H

#include <QWidget>
#include <QGraphicsScene>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QComboBox>
#include <QPainterPath>
#include <QGraphicsPathItem>
#include <QLabel>
#include <QStatusBar>
#include <cmath>
#include "CurveData.h"
#include "ZoomableGraphicsView.h"

struct ArcParams {
    QPointF center;
    double radius;
    double startAngle;
    double endAngle;
    bool valid;
    
    ArcParams() : valid(false) {}
    ArcParams(const QPointF& c, double r, double start, double end) 
        : center(c), radius(r), startAngle(start), endAngle(end), valid(true) {}
};

class GraphicsWidget : public QWidget
{
    Q_OBJECT

public:
    explicit GraphicsWidget(QWidget *parent = nullptr);

    // 绘制曲线数据
    void drawCurveData(const CurveData& curves);

    // 清除所有图形
    void clearScene();

    // 设置坐标缩放因子
    void setScaleFactor(double factor) { m_scaleFactor = factor; }

    // 设置坐标系转换模式
    void setCoordinateSystem(bool flipY) { m_flipY = flipY; }

private slots:
    void onDrawCurve1();
    void onDrawCurve2();
    void onDrawCurve3();
    void onClearScene();
    void onResetView();
    void onZoomChanged(double zoomLevel);
    void onToggleCoordinateSystem();

private:
    // 计算圆弧参数（从Python代码移植）
    ArcParams getArcParams(const QPointF& p0, const QPointF& p1, double radius);

    // 计算考虑坐标系转换的圆弧参数
    ArcParams getTransformedArcParams(const QPointF& p0_orig, const QPointF& p1_orig, double radius_orig);

    // 角度转换工具函数
    double radiansToDegrees(double radians);
    double degreesToRadians(double degrees);

    // 坐标缩放和转换函数
    QPointF scalePoint(const QPointF& point);
    double scaleRadius(double radius);
    QPointF transformPoint(const QPointF& point); // 坐标系转换

    // 计算最佳缩放因子
    void calculateOptimalScale(const CurveData& curves);

    // 更新状态显示
    void updateStatusDisplay();
    
    // UI组件
    ZoomableGraphicsView* m_view;
    QGraphicsScene* m_scene;
    QVBoxLayout* m_mainLayout;
    QHBoxLayout* m_buttonLayout;
    QPushButton* m_drawCurve1Btn;
    QPushButton* m_drawCurve2Btn;
    QPushButton* m_drawCurve3Btn;
    QPushButton* m_clearBtn;
    QPushButton* m_resetViewBtn;
    QPushButton* m_toggleCoordBtn;

    // 状态显示
    QLabel* m_statusLabel;

    // 绘制相关
    QPen m_pen;
    double m_scaleFactor;
    bool m_flipY; // 是否翻转Y轴以匹配笛卡尔坐标系
    double m_dataMinY, m_dataMaxY; // 用于Y轴翻转的数据范围
};

#endif // GRAPHICSWIDGET_H
