#ifndef CURVEDATA_H
#define CURVEDATA_H

#include <QPointF>
#include <vector>

struct CurvePoint {
    QPointF point;
    double radius;
    
    CurvePoint(double x, double y, double r) : point(x, y), radius(r) {}
    CurvePoint(const QPointF& p, double r) : point(p), radius(r) {}
};

using CurveData = std::vector<CurvePoint>;

// 预定义的曲线数据（从Python脚本移植）
namespace CurveDataSets {
    
    inline CurveData getCurves1() {
        return {
            CurvePoint(9794352.00, -0.67, 0.00),
            CurvePoint(0.00, 0.00, -10454268.67),
            CurvePoint(4778222.00, 8779192.67, -10454268.67),
            CurvePoint(181229.33, 17438526.00, 0.00),
            CurvePoint(180452.00, 17449800.00, 0.00),
            CurvePoint(9773872.67, 17449799.33, 0.00),
            CurvePoint(9794352.00, -0.67, 0.00)
        };
    }
    
    inline CurveData getCurves2() {
        return {
            CurvePoint(26017738.67, 17348200.0, 0.0),
            CurvePoint(25992338.67, 304800.0, 0.0),
            CurvePoint(21513800.0, 304800.0, 0.0),
            CurvePoint(21513800.0, 3362410.0, 6083300.0),
            CurvePoint(17360900.0, 9131300.0, 6083300.0),
            CurvePoint(21564600.0, 14916941.33, 0.0),
            CurvePoint(21564600.0, 17348200.0, 0.0),
            CurvePoint(26017738.67, 17348200.0, 0.0)
        };
    }
    
    inline CurveData getCurves3() {
        return {
            CurvePoint(177800.0, -0.667, -10489522.666999999),
            CurvePoint(5870337.3329999996, 8313088.667000004, -10489522.666999999),
            CurvePoint(533400.0, 17449800.0, 0.0),
            CurvePoint(9773872.666999994, 17449799.333000001, 0.0),
            CurvePoint(9794352.0, -0.667, 0.0)
        };
    }
}

#endif // CURVEDATA_H
