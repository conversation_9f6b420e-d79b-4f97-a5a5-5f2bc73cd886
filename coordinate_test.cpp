#include <iostream>
#include <cmath>

struct Point {
    double x, y;
    Point(double x = 0, double y = 0) : x(x), y(y) {}
};

// 模拟坐标转换
Point transformPoint(const Point& point, bool flipY, double dataMinY, double dataMaxY, double scaleFactor) {
    Point scaledPoint(point.x * scaleFactor, point.y * scaleFactor);
    
    if (flipY && (dataMaxY != dataMinY)) {
        double dataHeight = dataMaxY - dataMinY;
        double scaledHeight = dataHeight * scaleFactor;
        scaledPoint.y = scaledHeight - (scaledPoint.y - dataMinY * scaleFactor);
    }
    
    return scaledPoint;
}

// 测试圆弧角度计算
void testArcAngles() {
    std::cout << "=== 圆弧角度测试 ===" << std::endl;
    
    // 测试数据
    Point p0(0, 0);
    Point p1(100, 100);
    double radius = 50;
    
    // 计算圆心（简化版本）
    double dx = p1.x - p0.x;
    double dy = p1.y - p0.y;
    double chordLength = std::sqrt(dx * dx + dy * dy);
    
    if (std::abs(radius) >= chordLength / 2) {
        Point midPoint((p0.x + p1.x) / 2, (p0.y + p1.y) / 2);
        double h = std::sqrt(radius * radius - (chordLength / 2) * (chordLength / 2));
        
        double perpDx = dy / chordLength;
        double perpDy = -dx / chordLength;
        
        Point center;
        if (radius < 0) {
            center.x = midPoint.x - h * perpDx;
            center.y = midPoint.y - h * perpDy;
        } else {
            center.x = midPoint.x + h * perpDx;
            center.y = midPoint.y + h * perpDy;
        }
        
        // 计算角度
        double startAngle = std::atan2(p0.y - center.y, p0.x - center.x) * 180.0 / M_PI;
        double endAngle = std::atan2(p1.y - center.y, p1.x - center.x) * 180.0 / M_PI;
        
        std::cout << "原始坐标系:" << std::endl;
        std::cout << "  起点: (" << p0.x << ", " << p0.y << ")" << std::endl;
        std::cout << "  终点: (" << p1.x << ", " << p1.y << ")" << std::endl;
        std::cout << "  圆心: (" << center.x << ", " << center.y << ")" << std::endl;
        std::cout << "  起始角: " << startAngle << "°" << std::endl;
        std::cout << "  结束角: " << endAngle << "°" << std::endl;
        
        // 测试Y轴翻转后的效果
        double dataMinY = 0, dataMaxY = 100;
        double scaleFactor = 1.0;
        
        Point p0_flip = transformPoint(p0, true, dataMinY, dataMaxY, scaleFactor);
        Point p1_flip = transformPoint(p1, true, dataMinY, dataMaxY, scaleFactor);
        Point center_flip = transformPoint(center, true, dataMinY, dataMaxY, scaleFactor);
        
        double startAngle_flip = std::atan2(p0_flip.y - center_flip.y, p0_flip.x - center_flip.x) * 180.0 / M_PI;
        double endAngle_flip = std::atan2(p1_flip.y - center_flip.y, p1_flip.x - center_flip.x) * 180.0 / M_PI;
        
        std::cout << "\nY轴翻转后:" << std::endl;
        std::cout << "  起点: (" << p0_flip.x << ", " << p0_flip.y << ")" << std::endl;
        std::cout << "  终点: (" << p1_flip.x << ", " << p1_flip.y << ")" << std::endl;
        std::cout << "  圆心: (" << center_flip.x << ", " << center_flip.y << ")" << std::endl;
        std::cout << "  起始角: " << startAngle_flip << "°" << std::endl;
        std::cout << "  结束角: " << endAngle_flip << "°" << std::endl;
        
        // 分析角度变化
        std::cout << "\n角度变化分析:" << std::endl;
        std::cout << "  起始角变化: " << (startAngle_flip - startAngle) << "°" << std::endl;
        std::cout << "  结束角变化: " << (endAngle_flip - endAngle) << "°" << std::endl;
    }
}

void testCoordinateTransform() {
    std::cout << "\n=== 坐标转换测试 ===" << std::endl;
    
    // 测试数据范围
    double dataMinY = -0.67;
    double dataMaxY = 17449800.0;
    double scaleFactor = 1.0 / 10000.0;
    
    // 测试点
    Point testPoints[] = {
        Point(9794352.00, -0.67),
        Point(0.00, 0.00),
        Point(4778222.00, 8779192.67),
        Point(181229.33, 17438526.00)
    };
    
    std::cout << "数据范围: Y[" << dataMinY << ", " << dataMaxY << "]" << std::endl;
    std::cout << "缩放因子: " << scaleFactor << std::endl;
    std::cout << std::endl;
    
    for (int i = 0; i < 4; i++) {
        Point original = testPoints[i];
        Point qtCoord = transformPoint(original, false, dataMinY, dataMaxY, scaleFactor);
        Point cartesianCoord = transformPoint(original, true, dataMinY, dataMaxY, scaleFactor);
        
        std::cout << "点" << i << ":" << std::endl;
        std::cout << "  原始: (" << original.x << ", " << original.y << ")" << std::endl;
        std::cout << "  Qt坐标系: (" << qtCoord.x << ", " << qtCoord.y << ")" << std::endl;
        std::cout << "  笛卡尔坐标系: (" << cartesianCoord.x << ", " << cartesianCoord.y << ")" << std::endl;
        std::cout << "  Y差值: " << (cartesianCoord.y - qtCoord.y) << std::endl;
        std::cout << std::endl;
    }
}

int main() {
    std::cout << "坐标系转换和圆弧算法测试" << std::endl;
    std::cout << "=========================" << std::endl;
    
    testCoordinateTransform();
    testArcAngles();
    
    std::cout << "\n=== 修复建议 ===" << std::endl;
    std::cout << "1. 圆弧计算应在原始坐标系中进行" << std::endl;
    std::cout << "2. 只有最终绘制坐标需要转换" << std::endl;
    std::cout << "3. Y轴翻转会影响角度方向，需要调整" << std::endl;
    std::cout << "4. 圆弧的起始和结束角度可能需要交换" << std::endl;
    
    return 0;
}
