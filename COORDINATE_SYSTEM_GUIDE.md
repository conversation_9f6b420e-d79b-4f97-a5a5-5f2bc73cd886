# 坐标系转换指南

## 问题描述

您提出了一个非常重要的问题：**Qt坐标系与笛卡尔坐标系的差异**

### 坐标系差异

| 坐标系类型 | Y轴方向 | 原点位置 | 用途 |
|-----------|---------|----------|------|
| **笛卡尔坐标系** | Y轴向上为正 | 通常在左下角 | 数学、工程、CAD |
| **Qt坐标系** | Y轴向下为正 | 左上角 | 计算机图形、UI |

### 影响分析

从您的Python数据来看：
```
CurvePoint(9794352.00, -0.67, 0.00),     // Y值很小，接近0
CurvePoint(0.00, 0.00, -10454268.67),    // 原点
CurvePoint(4778222.00, 8779192.67, -10454268.67),  // Y值较大
CurvePoint(181229.33, 17438526.00, 0.00), // Y值最大
```

这些数据看起来是工程/CAD数据，应该使用笛卡尔坐标系显示。

## 解决方案

我已经实现了坐标系转换功能：

### 1. **自动Y轴翻转**
```cpp
QPointF GraphicsWidget::transformPoint(const QPointF& point)
{
    QPointF scaledPoint = scalePoint(point);
    
    if (m_flipY && (m_dataMaxY != m_dataMinY)) {
        // 翻转Y轴以匹配笛卡尔坐标系
        double dataHeight = m_dataMaxY - m_dataMinY;
        double scaledHeight = dataHeight * m_scaleFactor;
        
        // 翻转Y坐标
        scaledPoint.setY(scaledHeight - (scaledPoint.y() - m_dataMinY * m_scaleFactor));
    }
    
    return scaledPoint;
}
```

### 2. **坐标系切换按钮**
- **"笛卡尔坐标系"** - Y轴向上，适合工程数据
- **"Qt坐标系"** - Y轴向下，Qt默认方式

### 3. **智能数据范围计算**
- 自动计算数据的Y轴范围
- 基于数据范围进行正确的坐标转换
- 保持图形的正确比例和方向

## 使用建议

### 对于工程/CAD数据（推荐）
1. 使用 **"笛卡尔坐标系"** 模式
2. Y轴向上，符合工程习惯
3. 图形显示与原始设计意图一致

### 对于UI/图形界面数据
1. 使用 **"Qt坐标系"** 模式
2. Y轴向下，符合屏幕坐标习惯

## 技术实现

### 转换公式
```
笛卡尔坐标系转Qt坐标系:
qt_y = data_height - (cartesian_y - data_min_y)

Qt坐标系转笛卡尔坐标系:
cartesian_y = data_height - qt_y + data_min_y
```

### 关键特性
- ✅ **自动数据范围检测**
- ✅ **实时坐标系切换**
- ✅ **保持图形比例**
- ✅ **圆弧方向正确性**
- ✅ **缩放功能兼容**

## 圆弧方向的影响

坐标系翻转也会影响圆弧的方向：
- **正半径**: 在笛卡尔坐标系中为顺时针，在Qt坐标系中为逆时针
- **负半径**: 在笛卡尔坐标系中为逆时针，在Qt坐标系中为顺时针

我们的实现会自动处理这种差异，确保圆弧方向在两种坐标系中都正确显示。

## 验证方法

1. **绘制相同数据**在两种坐标系下
2. **观察Y轴方向**的差异
3. **检查圆弧方向**是否符合预期
4. **验证整体图形**是否与原始设计一致

## 默认设置

- **默认启用笛卡尔坐标系** (`m_flipY = true`)
- 适合大多数工程和CAD数据
- 可以随时通过按钮切换

这个解决方案确保了您的图形在Qt中显示时与原始的笛卡尔坐标系设计保持一致！
