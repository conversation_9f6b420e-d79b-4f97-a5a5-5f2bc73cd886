#include "ZoomableGraphicsView.h"
#include <QApplication>

ZoomableGraphicsView::ZoomableGraphicsView(QWidget* parent)
    : QGraphicsView(parent)
    , m_min<PERSON>oom(0.01)
    , m_max<PERSON><PERSON>(100.0)
    , m_zoomFactor(1.15)
{
    setRenderHint(QPainter::Antialiasing);
    setDragMode(QGraphicsView::ScrollHandDrag);
    setTransformationAnchor(QGraphicsView::AnchorUnderMouse);
    setResizeAnchor(QGraphicsView::AnchorUnderMouse);
    setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
}

ZoomableGraphicsView::ZoomableGraphicsView(QGraphicsScene* scene, QWidget* parent)
    : QGraphicsView(scene, parent)
    , m_min<PERSON>oom(0.01)
    , m_max<PERSON><PERSON>(100.0)
    , m_zoomFactor(1.15)
{
    setRenderHint(QPainter::Antialiasing);
    setDragMode(QGraphicsView::ScrollHandDrag);
    setTransformationAnchor(QGraphicsView::AnchorUnderMouse);
    setResizeAnchor(QGraphicsView::AnchorUnderMouse);
    setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
}

void ZoomableGraphicsView::wheelEvent(QWheelEvent* event)
{
    // 检查是否有图形内容
    if (!scene() || scene()->items().isEmpty()) {
        QGraphicsView::wheelEvent(event);
        return;
    }
    
    // 获取滚轮滚动的角度
    QPoint numDegrees = event->angleDelta() / 8;
    
    if (!numDegrees.isNull()) {
        QPoint numSteps = numDegrees / 15;
        
        // 计算缩放因子
        double scaleFactor = 1.0;
        if (numSteps.y() > 0) {
            // 向上滚动，放大
            scaleFactor = m_zoomFactor;
        } else if (numSteps.y() < 0) {
            // 向下滚动，缩小
            scaleFactor = 1.0 / m_zoomFactor;
        }
        
        // 获取当前的变换矩阵
        QTransform currentTransform = transform();
        double currentScale = currentTransform.m11(); // 获取当前缩放级别
        
        double newScale = currentScale * scaleFactor;
        
        // 限制缩放范围
        if (newScale >= m_minZoom && newScale <= m_maxZoom) {
            // 获取鼠标在视图中的位置
#if QT_VERSION >= QT_VERSION_CHECK(5, 14, 0)
            QPointF mousePos = event->position();
#else
            QPointF mousePos = event->posF();
#endif
            
            // 将鼠标位置转换为场景坐标
            QPointF scenePos = mapToScene(mousePos.toPoint());
            
            // 执行缩放
            scale(scaleFactor, scaleFactor);
            
            // 将鼠标位置重新映射到视图坐标
            QPointF newViewPos = mapFromScene(scenePos);
            
            // 调整视图位置，使鼠标位置保持不变
            QPointF delta = mousePos - newViewPos;
            translate(delta.x(), delta.y());
            
            // 发射缩放变化信号
            emit zoomChanged(newScale);
            
            qDebug() << QString("缩放: %1, 当前缩放级别: %2").arg(scaleFactor).arg(newScale);
        }
    }
    
    event->accept();
}
