# CurveDrawer - C++ Qt 曲线绘制器

这是一个从Python matplotlib项目转换而来的C++ Qt应用程序，用于绘制由直线段和圆弧段组成的复杂几何图形。

## 功能特性

- 使用QGraphicsView和QGraphicsScene进行高性能图形绘制
- 支持直线段和圆弧段的绘制
- 自动计算圆弧参数（圆心、半径、角度）
- 支持正负半径（顺时针/逆时针圆弧）
- 简单的用户界面，包含预设曲线数据
- 支持缩放和拖拽查看

## 系统要求

- Qt 6.x
- CMake 3.16+
- C++17 编译器
- Visual Studio 2019/2022 (Windows)

## 构建方法

### 方法1：使用提供的批处理文件
```bash
build.bat
```

### 方法2：手动构建
```bash
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022" -A x64
cmake --build . --config Release
```

## 使用方法

1. 运行编译后的可执行文件 `CurveDrawer.exe`
2. 点击界面上的按钮绘制不同的预设曲线：
   - "绘制曲线1" - 绘制第一组曲线数据
   - "绘制曲线2" - 绘制第二组曲线数据  
   - "绘制曲线3" - 绘制第三组曲线数据
   - "清除" - 清除当前绘制的图形
3. 使用鼠标拖拽来平移视图
4. 使用鼠标滚轮进行缩放

## 代码结构

- `main.cpp` - 应用程序入口点
- `GraphicsWidget.h/cpp` - 主要的图形绘制窗口类
- `CurveData.h` - 曲线数据结构定义和预设数据
- `CMakeLists.txt` - CMake构建配置

## 核心算法

### 圆弧参数计算
程序实现了从Python代码移植的圆弧参数计算算法：

1. 根据起点、终点和半径计算圆心位置
2. 处理正负半径（顺时针/逆时针方向）
3. 计算起始角度和结束角度
4. 使用QPainterPath::arcTo进行绘制

### 数据结构
```cpp
struct CurvePoint {
    QPointF point;  // 点坐标
    double radius;  // 到下一点的圆弧半径（0表示直线）
};
```

## 扩展使用

要添加自定义曲线数据，可以：

1. 在`CurveData.h`中添加新的数据集函数
2. 在`GraphicsWidget`中添加对应的按钮和槽函数
3. 或者直接调用`drawCurveData()`函数传入自定义的`CurveData`

## 注意事项

- 负半径表示逆时针圆弧
- 正半径表示顺时针圆弧
- 半径为0表示直线段
- 坐标系统与原Python代码保持一致
