#include <iostream>
#include <vector>
#include <cmath>

struct Point {
    double x, y;
    Point(double x = 0, double y = 0) : x(x), y(y) {}
};

struct CurvePoint {
    Point point;
    double radius;
    CurvePoint(double x, double y, double r) : point(x, y), radius(r) {}
};

using CurveData = std::vector<CurvePoint>;

// 预定义的曲线数据
CurveData getCurves1() {
    return {
        CurvePoint(9794352.00, -0.67, 0.00),
        CurvePoint(0.00, 0.00, -10454268.67),
        CurvePoint(4778222.00, 8779192.67, -10454268.67),
        CurvePoint(181229.33, 17438526.00, 0.00),
        CurvePoint(180452.00, 17449800.00, 0.00),
        CurvePoint(9773872.67, 17449799.33, 0.00),
        CurvePoint(9794352.00, -0.67, 0.00)
    };
}

struct ArcParams {
    Point center;
    double radius;
    double startAngle;
    double endAngle;
    bool valid;
    
    ArcParams() : valid(false) {}
    ArcParams(const Point& c, double r, double start, double end) 
        : center(c), radius(r), startAngle(start), endAngle(end), valid(true) {}
};

double radiansToDegrees(double radians) {
    return radians * 180.0 / M_PI;
}

ArcParams getArcParams(const Point& p0, const Point& p1, double radius) {
    // 计算弦长
    double dx = p1.x - p0.x;
    double dy = p1.y - p0.y;
    double chordLength = std::sqrt(dx * dx + dy * dy);
    
    if (std::abs(radius) < chordLength / 2) {
        return ArcParams(); // 无效的圆弧参数
    }
    
    // 计算弦中点
    Point midPoint((p0.x + p1.x) / 2, (p0.y + p1.y) / 2);
    
    // 计算从弦中点到圆心的距离
    double absRadius = std::abs(radius);
    double h = std::sqrt(absRadius * absRadius - (chordLength / 2) * (chordLength / 2));
    
    // 计算垂直于弦的单位向量
    double perpDx = dy / chordLength;
    double perpDy = -dx / chordLength;
    
    // 根据半径正负确定圆心
    Point center;
    if (radius < 0) {
        // 负半径：逆时针方向
        center.x = midPoint.x - h * perpDx;
        center.y = midPoint.y - h * perpDy;
    } else {
        // 正半径：顺时针方向
        center.x = midPoint.x + h * perpDx;
        center.y = midPoint.y + h * perpDy;
    }
    
    // 计算起止角（以度为单位）
    double startAngle = radiansToDegrees(std::atan2(p0.y - center.y, p0.x - center.x));
    double endAngle = radiansToDegrees(std::atan2(p1.y - center.y, p1.x - center.x));
    
    return ArcParams(center, absRadius, startAngle, endAngle);
}

Point scalePoint(const Point& point, double scaleFactor) {
    return Point(point.x * scaleFactor, point.y * scaleFactor);
}

double scaleRadius(double radius, double scaleFactor) {
    return radius * scaleFactor;
}

void analyzeCurveData(const CurveData& curves, const std::string& name) {
    std::cout << "\n=== 分析 " << name << " ===" << std::endl;
    std::cout << "点数量: " << curves.size() << std::endl;
    
    // 计算坐标范围
    double minX = curves[0].point.x, maxX = curves[0].point.x;
    double minY = curves[0].point.y, maxY = curves[0].point.y;
    
    for (const auto& cp : curves) {
        minX = std::min(minX, cp.point.x);
        maxX = std::max(maxX, cp.point.x);
        minY = std::min(minY, cp.point.y);
        maxY = std::max(maxY, cp.point.y);
    }
    
    std::cout << "X范围: [" << minX << ", " << maxX << "]" << std::endl;
    std::cout << "Y范围: [" << minY << ", " << maxY << "]" << std::endl;
    std::cout << "宽度: " << (maxX - minX) << ", 高度: " << (maxY - minY) << std::endl;
    
    // 建议的缩放因子
    double rangeX = maxX - minX;
    double rangeY = maxY - minY;
    double maxRange = std::max(rangeX, rangeY);
    double suggestedScale = 1000.0 / maxRange; // 缩放到1000单位内
    
    std::cout << "建议缩放因子: " << suggestedScale << std::endl;
    
    // 分析每个段
    for (size_t i = 0; i < curves.size() - 1; ++i) {
        const CurvePoint& current = curves[i];
        const CurvePoint& next = curves[i + 1];
        
        std::cout << "段" << i << ": ";
        std::cout << "起点=(" << current.point.x << ", " << current.point.y << "), ";
        std::cout << "终点=(" << next.point.x << ", " << next.point.y << "), ";
        std::cout << "半径=" << current.radius;
        
        if (std::abs(current.radius) < 1e-6) {
            std::cout << " [直线]" << std::endl;
        } else {
            std::cout << " [圆弧]" << std::endl;
            
            // 测试圆弧参数计算
            ArcParams arcParams = getArcParams(current.point, next.point, current.radius);
            if (arcParams.valid) {
                std::cout << "  圆心=(" << arcParams.center.x << ", " << arcParams.center.y << ")";
                std::cout << ", 半径=" << arcParams.radius;
                std::cout << ", 起始角=" << arcParams.startAngle;
                std::cout << ", 结束角=" << arcParams.endAngle << std::endl;
            } else {
                std::cout << "  [无效圆弧参数]" << std::endl;
            }
        }
    }
}

int main() {
    std::cout << "曲线数据分析工具" << std::endl;
    
    CurveData curves1 = getCurves1();
    analyzeCurveData(curves1, "曲线1");
    
    std::cout << "\n按任意键退出..." << std::endl;
    std::cin.get();
    
    return 0;
}
