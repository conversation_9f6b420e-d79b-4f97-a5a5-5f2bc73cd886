cmake_minimum_required(VERSION 3.16)
project(CurveDrawer)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)


# Try to find Qt6 first, then Qt5
find_package(Qt6 COMPONENTS Core Widgets)
if(NOT Qt6_FOUND)
    find_package(Qt5 REQUIRED COMPONENTS Core Widgets)
    set(QT_VERSION_MAJOR 5)
else()
    set(QT_VERSION_MAJOR 6)
endif()

# Enable automatic MOC, UIC, and RCC processing
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Add executable
add_executable(CurveDrawer
    main.cpp
    GraphicsWidget.cpp
    GraphicsWidget.h
    ZoomableGraphicsView.cpp
    ZoomableGraphicsView.h
    CurveData.h
)

# Link Qt libraries
if(QT_VERSION_MAJOR EQUAL 6)
    target_link_libraries(CurveDrawer Qt6::Core Qt6::Widgets)
else()
    target_link_libraries(CurveDrawer Qt5::Core Qt5::Widgets)
endif()

# Set output directory
set_target_properties(CurveDrawer PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)
