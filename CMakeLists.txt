cmake_minimum_required(VERSION 3.16)
project(CurveDrawer)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt6
find_package(Qt6 REQUIRED COMPONENTS Core Widgets)

# Enable automatic MOC, UIC, and RCC processing
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Add executable
add_executable(CurveDrawer
    main.cpp
    GraphicsWidget.cpp
    GraphicsWidget.h
    CurveData.h
)

# Link Qt libraries
target_link_libraries(CurveDrawer Qt6::Core Qt6::Widgets)

# Set output directory
set_target_properties(Curve<PERSON><PERSON>er PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)
